<?php
/**
 * Simple PWA Icon Creator for Flori Construction
 * Creates basic icons without complex graphics
 */

// Create icons directory
$iconsDir = __DIR__ . '/icons';
if (!is_dir($iconsDir)) {
    mkdir($iconsDir, 0755, true);
    echo "Created icons directory.\n";
}

// Icon sizes needed
$sizes = [72, 96, 128, 144, 152, 192, 384, 512];

// Create simple colored squares as placeholders
foreach ($sizes as $size) {
    $filename = "icon-{$size}x{$size}.png";
    $filepath = $iconsDir . '/' . $filename;
    
    // Create image
    $image = imagecreatetruecolor($size, $size);
    
    // Colors - Flori Construction brand colors
    $red = imagecolorallocate($image, 231, 76, 60);    // #e74c3c
    $white = imagecolorallocate($image, 255, 255, 255);
    $darkRed = imagecolorallocate($image, 192, 57, 43); // #c0392b
    
    // Fill background with gradient effect
    imagefill($image, 0, 0, $red);
    
    // Add border
    $borderSize = max(2, $size / 50);
    for ($i = 0; $i < $borderSize; $i++) {
        imagerectangle($image, $i, $i, $size - 1 - $i, $size - 1 - $i, $darkRed);
    }
    
    // Add "FC" text
    $fontSize = max(1, min(5, $size / 30));
    $text = 'FC';
    
    // Calculate text position (center)
    $textWidth = strlen($text) * imagefontwidth($fontSize);
    $textHeight = imagefontheight($fontSize);
    $x = ($size - $textWidth) / 2;
    $y = ($size - $textHeight) / 2;
    
    // Add text shadow
    imagestring($image, $fontSize, $x + 1, $y + 1, $text, $darkRed);
    // Add main text
    imagestring($image, $fontSize, $x, $y, $text, $white);
    
    // Save image
    if (imagepng($image, $filepath)) {
        echo "✅ Created: {$filename}\n";
    } else {
        echo "❌ Failed: {$filename}\n";
    }
    
    imagedestroy($image);
}

// Create shortcut icons
$shortcuts = [
    'shortcut-add.png' => ['color' => [39, 174, 96], 'text' => '+'],
    'shortcut-upload.png' => ['color' => [52, 152, 219], 'text' => '↑']
];

foreach ($shortcuts as $filename => $data) {
    $size = 96;
    $filepath = $iconsDir . '/' . $filename;
    
    $image = imagecreatetruecolor($size, $size);
    
    $bgColor = imagecolorallocate($image, $data['color'][0], $data['color'][1], $data['color'][2]);
    $white = imagecolorallocate($image, 255, 255, 255);
    $darkColor = imagecolorallocate($image, 
        max(0, $data['color'][0] - 40), 
        max(0, $data['color'][1] - 40), 
        max(0, $data['color'][2] - 40)
    );
    
    imagefill($image, 0, 0, $bgColor);
    
    // Add border
    for ($i = 0; $i < 2; $i++) {
        imagerectangle($image, $i, $i, $size - 1 - $i, $size - 1 - $i, $darkColor);
    }
    
    // Add symbol
    $fontSize = 5;
    $textWidth = strlen($data['text']) * imagefontwidth($fontSize);
    $textHeight = imagefontheight($fontSize);
    $x = ($size - $textWidth) / 2;
    $y = ($size - $textHeight) / 2;
    
    imagestring($image, $fontSize, $x + 1, $y + 1, $data['text'], $darkColor);
    imagestring($image, $fontSize, $x, $y, $data['text'], $white);
    
    if (imagepng($image, $filepath)) {
        echo "✅ Created: {$filename}\n";
    } else {
        echo "❌ Failed: {$filename}\n";
    }
    
    imagedestroy($image);
}

echo "\n🎉 Icon generation complete!\n";
echo "📁 Icons saved to: " . $iconsDir . "\n";
echo "📱 Your PWA is now ready for installation!\n\n";

echo "🧪 Test your PWA:\n";
echo "1. Open: http://localhost/erdevwe/mobile-app/\n";
echo "2. Look for 'Install' button in browser\n";
echo "3. Add to home screen on mobile\n\n";

// List created files
echo "📋 Created files:\n";
$files = scandir($iconsDir);
foreach ($files as $file) {
    if ($file !== '.' && $file !== '..') {
        $size = filesize($iconsDir . '/' . $file);
        echo "   • {$file} (" . number_format($size) . " bytes)\n";
    }
}
?>
