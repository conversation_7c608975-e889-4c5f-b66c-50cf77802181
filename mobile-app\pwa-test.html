<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Installation Test - Flori Construction</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #e74c3c;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #ecf0f1;
            border-radius: 10px;
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #c0392b;
        }
        
        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }
        
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .icon-item {
            text-align: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .icon-item img {
            max-width: 64px;
            max-height: 64px;
            border-radius: 4px;
        }
        
        code {
            background: #f1f2f6;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
        
        .test-results {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 PWA Installation Test</h1>
        <p style="text-align: center; color: #666;">Test your Flori Construction Admin PWA installation</p>
        
        <div class="test-section">
            <h2>📱 PWA Support Check</h2>
            <div id="pwa-support-status" class="status info">Checking PWA support...</div>
            <button class="btn" onclick="checkPWASupport()">🔍 Check PWA Support</button>
        </div>
        
        <div class="test-section">
            <h2>🎨 Icon Verification</h2>
            <div id="icon-status" class="status info">Checking icons...</div>
            <button class="btn" onclick="checkIcons()">🖼️ Verify Icons</button>
            <div id="icon-grid" class="icon-grid"></div>
        </div>
        
        <div class="test-section">
            <h2>📋 Manifest Validation</h2>
            <div id="manifest-status" class="status info">Checking manifest...</div>
            <button class="btn" onclick="checkManifest()">📄 Check Manifest</button>
            <div id="manifest-details"></div>
        </div>
        
        <div class="test-section">
            <h2>⚙️ Service Worker Status</h2>
            <div id="sw-status" class="status info">Checking service worker...</div>
            <button class="btn" onclick="checkServiceWorker()">🔧 Check Service Worker</button>
        </div>
        
        <div class="test-section">
            <h2>📲 Installation Test</h2>
            <div id="install-status" class="status info">Ready to test installation</div>
            <button class="btn" id="install-btn" onclick="installPWA()" disabled>📱 Install PWA</button>
            <button class="btn" onclick="openApp()">🚀 Open App</button>
        </div>
        
        <div class="test-section">
            <h2>📊 Test Results</h2>
            <div id="test-results" class="test-results">
                <p>Run the tests above to see results here.</p>
            </div>
        </div>
    </div>

    <script>
        let deferredPrompt;
        let testResults = [];
        
        // Listen for beforeinstallprompt event
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            document.getElementById('install-btn').disabled = false;
            updateStatus('install-status', 'PWA installation is available!', 'success');
        });
        
        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }
        
        function addTestResult(test, status, details = '') {
            testResults.push({ test, status, details });
            updateTestResults();
        }
        
        function updateTestResults() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<h3>Test Summary:</h3>';
            
            testResults.forEach(result => {
                const statusIcon = result.status === 'success' ? '✅' : result.status === 'error' ? '❌' : '⚠️';
                resultsDiv.innerHTML += `
                    <div class="status ${result.status}">
                        ${statusIcon} <strong>${result.test}:</strong> ${result.details}
                    </div>
                `;
            });
        }
        
        async function checkPWASupport() {
            updateStatus('pwa-support-status', 'Checking PWA support...', 'info');
            
            const checks = {
                serviceWorker: 'serviceWorker' in navigator,
                manifest: 'manifest' in document.createElement('link'),
                beforeInstallPrompt: 'onbeforeinstallprompt' in window,
                pushNotifications: 'PushManager' in window,
                notifications: 'Notification' in window
            };
            
            const supportedFeatures = Object.values(checks).filter(Boolean).length;
            const totalFeatures = Object.keys(checks).length;
            
            if (supportedFeatures === totalFeatures) {
                updateStatus('pwa-support-status', `✅ Full PWA support (${supportedFeatures}/${totalFeatures} features)`, 'success');
                addTestResult('PWA Support', 'success', `All ${totalFeatures} PWA features supported`);
            } else {
                updateStatus('pwa-support-status', `⚠️ Partial PWA support (${supportedFeatures}/${totalFeatures} features)`, 'warning');
                addTestResult('PWA Support', 'warning', `${supportedFeatures}/${totalFeatures} features supported`);
            }
        }
        
        async function checkIcons() {
            updateStatus('icon-status', 'Checking icons...', 'info');
            
            const iconSizes = [72, 96, 128, 144, 152, 192, 384, 512];
            const shortcuts = ['shortcut-add.png', 'shortcut-upload.png'];
            const iconGrid = document.getElementById('icon-grid');
            iconGrid.innerHTML = '';
            
            let successCount = 0;
            let totalIcons = iconSizes.length + shortcuts.length;
            
            // Check main icons
            for (const size of iconSizes) {
                const iconUrl = `./icons/icon-${size}x${size}.png`;
                try {
                    const response = await fetch(iconUrl);
                    if (response.ok) {
                        successCount++;
                        iconGrid.innerHTML += `
                            <div class="icon-item">
                                <img src="${iconUrl}" alt="${size}x${size}">
                                <div>${size}x${size}</div>
                                <div style="color: green;">✅</div>
                            </div>
                        `;
                    } else {
                        iconGrid.innerHTML += `
                            <div class="icon-item">
                                <div style="color: red;">❌</div>
                                <div>${size}x${size}</div>
                                <div>Missing</div>
                            </div>
                        `;
                    }
                } catch (error) {
                    iconGrid.innerHTML += `
                        <div class="icon-item">
                            <div style="color: red;">❌</div>
                            <div>${size}x${size}</div>
                            <div>Error</div>
                        </div>
                    `;
                }
            }
            
            // Check shortcut icons
            for (const shortcut of shortcuts) {
                const iconUrl = `./icons/${shortcut}`;
                try {
                    const response = await fetch(iconUrl);
                    if (response.ok) {
                        successCount++;
                        iconGrid.innerHTML += `
                            <div class="icon-item">
                                <img src="${iconUrl}" alt="${shortcut}">
                                <div>${shortcut}</div>
                                <div style="color: green;">✅</div>
                            </div>
                        `;
                    } else {
                        iconGrid.innerHTML += `
                            <div class="icon-item">
                                <div style="color: red;">❌</div>
                                <div>${shortcut}</div>
                                <div>Missing</div>
                            </div>
                        `;
                    }
                } catch (error) {
                    iconGrid.innerHTML += `
                        <div class="icon-item">
                            <div style="color: red;">❌</div>
                            <div>${shortcut}</div>
                            <div>Error</div>
                        </div>
                    `;
                }
            }
            
            if (successCount === totalIcons) {
                updateStatus('icon-status', `✅ All ${totalIcons} icons found`, 'success');
                addTestResult('Icons', 'success', `All ${totalIcons} icons are available`);
            } else {
                updateStatus('icon-status', `⚠️ ${successCount}/${totalIcons} icons found`, 'warning');
                addTestResult('Icons', 'warning', `${successCount}/${totalIcons} icons found`);
            }
        }
        
        async function checkManifest() {
            updateStatus('manifest-status', 'Checking manifest...', 'info');
            
            try {
                const response = await fetch('./manifest.json');
                if (response.ok) {
                    const manifest = await response.json();
                    
                    const manifestDetails = document.getElementById('manifest-details');
                    manifestDetails.innerHTML = `
                        <div style="margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                            <h4>Manifest Details:</h4>
                            <p><strong>Name:</strong> ${manifest.name}</p>
                            <p><strong>Short Name:</strong> ${manifest.short_name}</p>
                            <p><strong>Start URL:</strong> ${manifest.start_url}</p>
                            <p><strong>Display:</strong> ${manifest.display}</p>
                            <p><strong>Theme Color:</strong> ${manifest.theme_color}</p>
                            <p><strong>Icons:</strong> ${manifest.icons.length} defined</p>
                            <p><strong>Shortcuts:</strong> ${manifest.shortcuts.length} defined</p>
                        </div>
                    `;
                    
                    updateStatus('manifest-status', '✅ Manifest loaded successfully', 'success');
                    addTestResult('Manifest', 'success', 'Manifest file is valid and accessible');
                } else {
                    updateStatus('manifest-status', '❌ Manifest not found', 'error');
                    addTestResult('Manifest', 'error', 'Manifest file not accessible');
                }
            } catch (error) {
                updateStatus('manifest-status', '❌ Manifest error: ' + error.message, 'error');
                addTestResult('Manifest', 'error', 'Error loading manifest: ' + error.message);
            }
        }
        
        async function checkServiceWorker() {
            updateStatus('sw-status', 'Checking service worker...', 'info');
            
            if ('serviceWorker' in navigator) {
                try {
                    const registration = await navigator.serviceWorker.getRegistration();
                    if (registration) {
                        updateStatus('sw-status', '✅ Service worker is registered and active', 'success');
                        addTestResult('Service Worker', 'success', 'Service worker is registered and running');
                    } else {
                        updateStatus('sw-status', '⚠️ Service worker not registered', 'warning');
                        addTestResult('Service Worker', 'warning', 'Service worker not registered');
                    }
                } catch (error) {
                    updateStatus('sw-status', '❌ Service worker error: ' + error.message, 'error');
                    addTestResult('Service Worker', 'error', 'Service worker error: ' + error.message);
                }
            } else {
                updateStatus('sw-status', '❌ Service workers not supported', 'error');
                addTestResult('Service Worker', 'error', 'Service workers not supported in this browser');
            }
        }
        
        async function installPWA() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                const { outcome } = await deferredPrompt.userChoice;
                
                if (outcome === 'accepted') {
                    updateStatus('install-status', '✅ PWA installed successfully!', 'success');
                    addTestResult('Installation', 'success', 'PWA was installed successfully');
                } else {
                    updateStatus('install-status', '⚠️ PWA installation declined', 'warning');
                    addTestResult('Installation', 'warning', 'User declined PWA installation');
                }
                
                deferredPrompt = null;
                document.getElementById('install-btn').disabled = true;
            } else {
                updateStatus('install-status', '❌ PWA installation not available', 'error');
                addTestResult('Installation', 'error', 'PWA installation prompt not available');
            }
        }
        
        function openApp() {
            window.open('./index.html', '_blank');
        }
        
        // Auto-run basic checks on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                checkPWASupport();
                checkIcons();
                checkManifest();
                checkServiceWorker();
            }, 1000);
        });
    </script>
</body>
</html>
