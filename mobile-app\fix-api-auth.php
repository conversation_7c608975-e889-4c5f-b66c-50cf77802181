<?php
/**
 * Fix API Authentication Issues
 * Updates all API files to use unified authentication helper
 */

echo "<h1>🔧 API Authentication Fix</h1>\n";
echo "<p>Fixing authentication issues across all API endpoints...</p>\n";

$apiDir = dirname(__DIR__) . '/api';
$fixes = [];

// List of API files that need authentication fixes
$apiFiles = [
    'content.php',
    'services.php',
    'notifications.php',
    'inquiries.php',
    'users.php',
    'projects.php',
    'media.php'
];

echo "<h2>📋 Fixing API Files</h2>\n";

foreach ($apiFiles as $file) {
    $filePath = $apiDir . '/' . $file;
    
    if (!file_exists($filePath)) {
        echo "<p>❌ File not found: {$file}</p>\n";
        continue;
    }
    
    echo "<p>🔧 Processing: {$file}</p>\n";
    
    $content = file_get_contents($filePath);
    $originalContent = $content;
    
    // Check if auth-helper is already included
    if (strpos($content, 'auth-helper.php') === false) {
        // Add auth-helper include after config include
        $content = preg_replace(
            '/require_once.*config\.php.*\n/',
            "$0require_once __DIR__ . '/auth-helper.php';\n",
            $content
        );
        
        if ($content !== $originalContent) {
            $fixes[] = "Added auth-helper.php include to {$file}";
        }
    }
    
    // Remove duplicate authentication functions
    $functionsToRemove = [
        'authenticateRequest',
        'getBearerToken',
        'getAuthorizationHeader'
    ];
    
    foreach ($functionsToRemove as $func) {
        // Remove function definition (including comments and body)
        $pattern = '/\/\*\*.*?\*\/\s*function\s+' . $func . '\s*\([^{]*\)\s*\{[^}]*(?:\{[^}]*\}[^}]*)*\}/s';
        $newContent = preg_replace($pattern, '', $content);
        
        if ($newContent !== $content) {
            $content = $newContent;
            $fixes[] = "Removed duplicate {$func} function from {$file}";
        }
        
        // Also remove simpler function definitions
        $pattern = '/function\s+' . $func . '\s*\([^{]*\)\s*\{[^}]*(?:\{[^}]*\}[^}]*)*\}/s';
        $newContent = preg_replace($pattern, '', $content);
        
        if ($newContent !== $content) {
            $content = $newContent;
            $fixes[] = "Removed {$func} function from {$file}";
        }
    }
    
    // Clean up extra whitespace
    $content = preg_replace('/\n\s*\n\s*\n/', "\n\n", $content);
    
    // Write back if changed
    if ($content !== $originalContent) {
        file_put_contents($filePath, $content);
        echo "<p>✅ Updated: {$file}</p>\n";
    } else {
        echo "<p>ℹ️ No changes needed: {$file}</p>\n";
    }
}

// Fix auth.php to use the helper for consistency
echo "<h2>🔧 Updating auth.php</h2>\n";
$authFile = $apiDir . '/auth.php';

if (file_exists($authFile)) {
    $authContent = file_get_contents($authFile);
    
    // Add auth-helper include if not present
    if (strpos($authContent, 'auth-helper.php') === false) {
        $authContent = preg_replace(
            '/require_once.*config\.php.*\n/',
            "$0require_once __DIR__ . '/auth-helper.php';\n",
            $authContent
        );
        
        file_put_contents($authFile, $authContent);
        $fixes[] = "Added auth-helper.php include to auth.php";
        echo "<p>✅ Updated auth.php</p>\n";
    } else {
        echo "<p>ℹ️ auth.php already includes auth-helper</p>\n";
    }
}

// Create database table for auth attempts if it doesn't exist
echo "<h2>📊 Setting up auth_attempts table</h2>\n";

try {
    require_once dirname(__DIR__) . '/config/config.php';
    
    $createTable = "
    CREATE TABLE IF NOT EXISTS auth_attempts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        identifier VARCHAR(255) NOT NULL,
        success TINYINT(1) NOT NULL DEFAULT 0,
        attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        ip_address VARCHAR(45),
        user_agent TEXT,
        INDEX idx_identifier_time (identifier, attempted_at),
        INDEX idx_attempted_at (attempted_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $db->query($createTable);
    echo "<p>✅ auth_attempts table created/verified</p>\n";
    $fixes[] = "Created/verified auth_attempts table";
    
} catch (Exception $e) {
    echo "<p>❌ Database error: " . $e->getMessage() . "</p>\n";
}

// Test API endpoints
echo "<h2>🧪 Testing API Endpoints</h2>\n";

$testEndpoints = [
    'auth.php?action=ping',
    'mobile.php?action=dashboard',
    'projects.php',
    'services.php',
    'content.php'
];

foreach ($testEndpoints as $endpoint) {
    $url = "http://localhost/erdevwe/api/{$endpoint}";
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 5,
            'ignore_errors' => true
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    
    if ($response !== false) {
        $data = json_decode($response, true);
        if (json_last_error() === JSON_ERROR_NONE) {
            echo "<p>✅ {$endpoint}: Responding correctly</p>\n";
        } else {
            echo "<p>⚠️ {$endpoint}: Response not JSON</p>\n";
        }
    } else {
        echo "<p>❌ {$endpoint}: Not accessible</p>\n";
    }
}

// Summary
echo "<h2>📋 Summary</h2>\n";
echo "<p><strong>Fixes Applied:</strong></p>\n";
echo "<ul>\n";
foreach ($fixes as $fix) {
    echo "<li>✅ {$fix}</li>\n";
}
echo "</ul>\n";

echo "<h2>🎯 Next Steps</h2>\n";
echo "<ol>\n";
echo "<li>Test authentication in mobile app</li>\n";
echo "<li>Verify all API endpoints work correctly</li>\n";
echo "<li>Check browser console for any remaining errors</li>\n";
echo "<li>Test offline functionality</li>\n";
echo "</ol>\n";

echo "<h2>🔗 Test Links</h2>\n";
echo "<ul>\n";
echo "<li><a href='../mobile-app/api-integration-test.html'>API Integration Test</a></li>\n";
echo "<li><a href='../mobile-app/index.html'>Mobile App</a></li>\n";
echo "<li><a href='../api-test.php'>API Test Page</a></li>\n";
echo "</ul>\n";

echo "<div style='margin-top: 30px; padding: 20px; background: #d4edda; border-radius: 8px;'>\n";
echo "<strong>✅ API Authentication Fix Complete!</strong><br>\n";
echo "All API endpoints now use unified authentication with improved error handling and logging.\n";
echo "</div>\n";
?>
