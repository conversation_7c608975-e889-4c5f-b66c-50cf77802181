<?php
/**
 * PWA Icon Generator for Flori Construction Admin App
 * Generates all required PWA icons programmatically
 */

// Ensure GD extension is loaded
if (!extension_loaded('gd')) {
    die('GD extension is required for icon generation.');
}

// Create icons directory if it doesn't exist
$iconsDir = __DIR__ . '/icons';
if (!is_dir($iconsDir)) {
    mkdir($iconsDir, 0755, true);
}

// Icon sizes to generate
$iconSizes = [
    72 => 'icon-72x72.png',
    96 => 'icon-96x96.png',
    128 => 'icon-128x128.png',
    144 => 'icon-144x144.png',
    152 => 'icon-152x152.png',
    192 => 'icon-192x192.png',
    384 => 'icon-384x384.png',
    512 => 'icon-512x512.png'
];

// Shortcut icons
$shortcutIcons = [
    'shortcut-add.png' => ['size' => 96, 'color' => [39, 174, 96], 'symbol' => '+'],
    'shortcut-upload.png' => ['size' => 96, 'color' => [52, 152, 219], 'symbol' => '↑']
];

/**
 * Create a PWA icon with gradient background and text
 */
function createIcon($size, $isShortcut = false, $shortcutData = null) {
    // Create image
    $image = imagecreatetruecolor($size, $size);
    
    // Enable alpha blending
    imagealphablending($image, true);
    imagesavealpha($image, true);
    
    if ($isShortcut && $shortcutData) {
        // Shortcut icon colors
        $color1 = imagecolorallocate($image, $shortcutData['color'][0], $shortcutData['color'][1], $shortcutData['color'][2]);
        $color2 = imagecolorallocate($image, 
            max(0, $shortcutData['color'][0] - 30), 
            max(0, $shortcutData['color'][1] - 30), 
            max(0, $shortcutData['color'][2] - 30)
        );
    } else {
        // Main app icon colors (Flori Construction red)
        $color1 = imagecolorallocate($image, 231, 76, 60);  // #e74c3c
        $color2 = imagecolorallocate($image, 192, 57, 43);  // #c0392b
    }
    
    // Create gradient background
    for ($y = 0; $y < $size; $y++) {
        $ratio = $y / $size;
        $r = $color1[0] + ($color2[0] - $color1[0]) * $ratio;
        $g = $color1[1] + ($color2[1] - $color1[1]) * $ratio;
        $b = $color1[2] + ($color2[2] - $color1[2]) * $ratio;
        
        // Get RGB values from color allocations
        $r1 = ($color1 >> 16) & 0xFF;
        $g1 = ($color1 >> 8) & 0xFF;
        $b1 = $color1 & 0xFF;
        
        $r2 = ($color2 >> 16) & 0xFF;
        $g2 = ($color2 >> 8) & 0xFF;
        $b2 = $color2 & 0xFF;
        
        $r = $r1 + ($r2 - $r1) * $ratio;
        $g = $g1 + ($g2 - $g1) * $ratio;
        $b = $b1 + ($b2 - $b1) * $ratio;
        
        $lineColor = imagecolorallocate($image, $r, $g, $b);
        imageline($image, 0, $y, $size - 1, $y, $lineColor);
    }
    
    // Add rounded corners (simple approach)
    $radius = $size * 0.15;
    $transparent = imagecolorallocatealpha($image, 0, 0, 0, 127);
    
    // Top-left corner
    for ($x = 0; $x < $radius; $x++) {
        for ($y = 0; $y < $radius; $y++) {
            $distance = sqrt(($x - $radius) * ($x - $radius) + ($y - $radius) * ($y - $radius));
            if ($distance > $radius) {
                imagesetpixel($image, $x, $y, $transparent);
            }
        }
    }
    
    // Top-right corner
    for ($x = $size - $radius; $x < $size; $x++) {
        for ($y = 0; $y < $radius; $y++) {
            $distance = sqrt(($x - ($size - $radius)) * ($x - ($size - $radius)) + ($y - $radius) * ($y - $radius));
            if ($distance > $radius) {
                imagesetpixel($image, $x, $y, $transparent);
            }
        }
    }
    
    // Bottom-left corner
    for ($x = 0; $x < $radius; $x++) {
        for ($y = $size - $radius; $y < $size; $y++) {
            $distance = sqrt(($x - $radius) * ($x - $radius) + ($y - ($size - $radius)) * ($y - ($size - $radius)));
            if ($distance > $radius) {
                imagesetpixel($image, $x, $y, $transparent);
            }
        }
    }
    
    // Bottom-right corner
    for ($x = $size - $radius; $x < $size; $x++) {
        for ($y = $size - $radius; $y < $size; $y++) {
            $distance = sqrt(($x - ($size - $radius)) * ($x - ($size - $radius)) + ($y - ($size - $radius)) * ($y - ($size - $radius)));
            if ($distance > $radius) {
                imagesetpixel($image, $x, $y, $transparent);
            }
        }
    }
    
    // Add text
    $white = imagecolorallocate($image, 255, 255, 255);
    
    if ($isShortcut && $shortcutData) {
        // Add shortcut symbol
        $fontSize = $size * 0.4;
        $fontFile = null; // Use built-in font
        
        // Calculate text position
        $textBox = imagettfbbox($fontSize, 0, $fontFile, $shortcutData['symbol']);
        $textWidth = $textBox[4] - $textBox[0];
        $textHeight = $textBox[1] - $textBox[7];
        $x = ($size - $textWidth) / 2;
        $y = ($size - $textHeight) / 2 + $textHeight;
        
        // Use imagestring for built-in font
        $x = ($size - strlen($shortcutData['symbol']) * 10) / 2;
        $y = ($size - 15) / 2;
        imagestring($image, 5, $x, $y, $shortcutData['symbol'], $white);
    } else {
        // Add "FC" text for main icon
        $fontSize = 5; // Built-in font size
        $text = 'FC';
        
        // Calculate position for centered text
        $textWidth = strlen($text) * imagefontwidth($fontSize);
        $textHeight = imagefontheight($fontSize);
        $x = ($size - $textWidth) / 2;
        $y = ($size - $textHeight) / 2;
        
        imagestring($image, $fontSize, $x, $y, $text, $white);
        
        // Add small construction emoji representation
        $smallText = 'BUILD';
        $smallFontSize = 2;
        $smallTextWidth = strlen($smallText) * imagefontwidth($smallFontSize);
        $smallX = ($size - $smallTextWidth) / 2;
        $smallY = $y + $textHeight + 10;
        
        if ($smallY + imagefontheight($smallFontSize) < $size) {
            imagestring($image, $smallFontSize, $smallX, $smallY, $smallText, $white);
        }
    }
    
    return $image;
}

/**
 * Generate all PWA icons
 */
function generateAllIcons() {
    global $iconSizes, $shortcutIcons, $iconsDir;
    
    $generated = [];
    
    echo "<h1>🎨 PWA Icon Generator</h1>\n";
    echo "<p>Generating PWA icons for Flori Construction Admin App...</p>\n";
    echo "<div style='font-family: monospace; background: #f4f4f4; padding: 20px; border-radius: 8px;'>\n";
    
    // Generate main app icons
    foreach ($iconSizes as $size => $filename) {
        echo "Generating {$filename} ({$size}x{$size})... ";
        
        $image = createIcon($size);
        $filepath = $iconsDir . '/' . $filename;
        
        if (imagepng($image, $filepath)) {
            echo "✅ Success\n<br>";
            $generated[] = $filename;
        } else {
            echo "❌ Failed\n<br>";
        }
        
        imagedestroy($image);
    }
    
    // Generate shortcut icons
    foreach ($shortcutIcons as $filename => $data) {
        echo "Generating {$filename} ({$data['size']}x{$data['size']})... ";
        
        $image = createIcon($data['size'], true, $data);
        $filepath = $iconsDir . '/' . $filename;
        
        if (imagepng($image, $filepath)) {
            echo "✅ Success\n<br>";
            $generated[] = $filename;
        } else {
            echo "❌ Failed\n<br>";
        }
        
        imagedestroy($image);
    }
    
    echo "</div>\n";
    echo "<h2>📊 Generation Summary</h2>\n";
    echo "<p><strong>Generated " . count($generated) . " icons:</strong></p>\n";
    echo "<ul>\n";
    foreach ($generated as $filename) {
        echo "<li>✅ {$filename}</li>\n";
    }
    echo "</ul>\n";
    
    echo "<h2>📱 Next Steps</h2>\n";
    echo "<ol>\n";
    echo "<li>Icons have been saved to <code>mobile-app/icons/</code> directory</li>\n";
    echo "<li>Your PWA manifest.json is already configured</li>\n";
    echo "<li>Test PWA installation on your mobile device</li>\n";
    echo "<li>Check browser developer tools for any PWA issues</li>\n";
    echo "</ol>\n";
    
    return $generated;
}

// Set content type to HTML
header('Content-Type: text/html; charset=utf-8');

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Icon Generator - Flori Construction</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #e74c3c;
            text-align: center;
        }
        
        code {
            background: #f1f2f6;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }
        
        .success {
            color: #27ae60;
        }
        
        .error {
            color: #e74c3c;
        }
    </style>
</head>
<body>
    <div class="container">
        <?php
        // Generate icons
        $generated = generateAllIcons();
        ?>
        
        <h2>🔧 PWA Installation Test</h2>
        <p>To test your PWA installation:</p>
        <ol>
            <li>Open your mobile app in Chrome: <code>http://localhost/erdevwe/mobile-app/</code></li>
            <li>Look for the "Install" prompt or "Add to Home Screen" option</li>
            <li>Install the app and test offline functionality</li>
        </ol>
        
        <h2>📂 Generated Files</h2>
        <p>Check the <code>mobile-app/icons/</code> directory for all generated icons.</p>
        
        <div style="margin-top: 30px; padding: 20px; background: #e8f5e8; border-radius: 8px;">
            <strong>✅ PWA Setup Complete!</strong><br>
            Your Flori Construction Admin App is now ready for installation as a Progressive Web App.
        </div>
    </div>
</body>
</html>
<?php
?>
