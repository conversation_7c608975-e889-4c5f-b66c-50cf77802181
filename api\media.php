<?php
/**
 * Media Upload API for Flori Construction Ltd Mobile App
 * Handles file uploads and media management
 */

require_once dirname(__DIR__) . '/config/config.php';
require_once __DIR__ . '/auth.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            handleGetMedia();
            break;

        case 'POST':
            $user = authenticateRequest();
            if (!$user) {
                jsonResponse(['error' => 'Authentication required'], 401);
            }
            handleUploadMedia($user);
            break;

        case 'DELETE':
            $user = authenticateRequest();
            if (!$user) {
                jsonResponse(['error' => 'Authentication required'], 401);
            }
            handleDeleteMedia($user);
            break;

        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
    }
} catch (Exception $e) {
    jsonResponse(['error' => $e->getMessage()], 500);
}

function handleGetMedia() {
    global $db;

    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
    $type = isset($_GET['type']) ? sanitize($_GET['type']) : '';

    $offset = ($page - 1) * $limit;

    // Build query
    $where = ['m.is_active = 1'];
    $params = [];

    if ($type) {
        if ($type === 'image') {
            $where[] = 'm.file_type IN ("jpg", "jpeg", "png", "gif", "webp", "svg")';
        } elseif ($type === 'video') {
            $where[] = 'm.file_type IN ("mp4", "webm", "ogg")';
        }
    }

    $whereClause = implode(' AND ', $where);

    // Get total count
    $totalQuery = "SELECT COUNT(*) as total FROM media m WHERE {$whereClause}";
    $totalResult = $db->fetchOne($totalQuery, $params);
    $total = $totalResult['total'];

    // Get media files
    $query = "SELECT m.*, u.full_name as uploaded_by_name
              FROM media m
              LEFT JOIN users u ON m.uploaded_by = u.id
              WHERE {$whereClause}
              ORDER BY m.created_at DESC
              LIMIT {$limit} OFFSET {$offset}";

    $mediaFiles = $db->fetchAll($query, $params);

    // Add full URLs
    foreach ($mediaFiles as &$file) {
        $file['url'] = UPLOAD_URL . '/' . $file['file_path'];
        $file['thumbnail_url'] = generateThumbnailUrl($file['file_path'], $file['file_type']);
    }

    jsonResponse([
        'success' => true,
        'media' => $mediaFiles,
        'pagination' => [
            'page' => $page,
            'limit' => $limit,
            'total' => $total,
            'pages' => ceil($total / $limit)
        ]
    ]);
}

function handleUploadMedia($user) {
    global $db;

    if (!isset($_FILES['file'])) {
        jsonResponse(['error' => 'No file uploaded'], 400);
    }

    $file = $_FILES['file'];
    $directory = isset($_POST['directory']) ? sanitize($_POST['directory']) : 'general';
    $altText = isset($_POST['alt_text']) ? sanitize($_POST['alt_text']) : '';
    $caption = isset($_POST['caption']) ? sanitize($_POST['caption']) : '';

    try {
        // Upload file
        $uploadResult = uploadFile($file, $directory);

        // Get file type
        $fileType = strtolower(pathinfo($uploadResult['original_name'], PATHINFO_EXTENSION));

        // Insert into database
        $mediaData = [
            'filename' => $uploadResult['filename'],
            'original_name' => $uploadResult['original_name'],
            'file_path' => $uploadResult['file_path'],
            'file_type' => $fileType,
            'file_size' => $uploadResult['file_size'],
            'mime_type' => $uploadResult['mime_type'],
            'alt_text' => $altText,
            'caption' => $caption,
            'uploaded_by' => $user['id']
        ];

        $mediaId = $db->insert('media', $mediaData);

        // Get the inserted media record
        $media = $db->fetchOne("SELECT * FROM media WHERE id = ?", [$mediaId]);
        $media['url'] = UPLOAD_URL . '/' . $media['file_path'];
        $media['thumbnail_url'] = generateThumbnailUrl($media['file_path'], $media['file_type']);

        // Generate thumbnail for raster images (SVG doesn't need thumbnails)
        if (in_array($fileType, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
            generateThumbnail($uploadResult['file_path']);
        }

        jsonResponse([
            'success' => true,
            'message' => 'File uploaded successfully',
            'media' => $media
        ]);

    } catch (Exception $e) {
        jsonResponse(['error' => $e->getMessage()], 400);
    }
}

function handleDeleteMedia($user) {
    global $db;

    if (!isset($_GET['id'])) {
        jsonResponse(['error' => 'Media ID required'], 400);
    }

    $mediaId = (int)$_GET['id'];

    // Get media file
    $media = $db->fetchOne("SELECT * FROM media WHERE id = ? AND is_active = 1", [$mediaId]);
    if (!$media) {
        jsonResponse(['error' => 'Media file not found'], 404);
    }

    // Check if user owns the file or is admin
    if ($media['uploaded_by'] != $user['id'] && $user['role'] !== 'admin') {
        jsonResponse(['error' => 'Permission denied'], 403);
    }

    // Soft delete from database
    $db->update('media',
        ['is_active' => 0],
        'id = ?',
        [$mediaId]
    );

    // Optionally delete physical file (uncomment if needed)
    /*
    $filePath = UPLOAD_PATH . '/' . $media['file_path'];
    if (file_exists($filePath)) {
        unlink($filePath);
    }

    // Delete thumbnail if exists
    $thumbnailPath = getThumbnailPath($media['file_path']);
    if (file_exists($thumbnailPath)) {
        unlink($thumbnailPath);
    }
    */

    jsonResponse([
        'success' => true,
        'message' => 'Media file deleted successfully'
    ]);
}

function generateThumbnail($filePath) {
    $fullPath = UPLOAD_PATH . '/' . $filePath;
    $thumbnailDir = UPLOAD_PATH . '/thumbnails/' . dirname($filePath);

    // Create thumbnail directory if it doesn't exist
    if (!is_dir($thumbnailDir)) {
        mkdir($thumbnailDir, 0755, true);
    }

    $thumbnailPath = $thumbnailDir . '/' . basename($filePath);

    // Get image info
    $imageInfo = getimagesize($fullPath);
    if (!$imageInfo) {
        return false;
    }

    $originalWidth = $imageInfo[0];
    $originalHeight = $imageInfo[1];
    $imageType = $imageInfo[2];

    // Calculate thumbnail dimensions (max 300x300)
    $maxSize = 300;
    if ($originalWidth > $originalHeight) {
        $thumbnailWidth = $maxSize;
        $thumbnailHeight = ($originalHeight * $maxSize) / $originalWidth;
    } else {
        $thumbnailHeight = $maxSize;
        $thumbnailWidth = ($originalWidth * $maxSize) / $originalHeight;
    }

    // Create image resource based on type
    switch ($imageType) {
        case IMAGETYPE_JPEG:
            $sourceImage = imagecreatefromjpeg($fullPath);
            break;
        case IMAGETYPE_PNG:
            $sourceImage = imagecreatefrompng($fullPath);
            break;
        case IMAGETYPE_GIF:
            $sourceImage = imagecreatefromgif($fullPath);
            break;
        case IMAGETYPE_WEBP:
            $sourceImage = imagecreatefromwebp($fullPath);
            break;
        default:
            return false;
    }

    // Create thumbnail
    $thumbnail = imagecreatetruecolor($thumbnailWidth, $thumbnailHeight);

    // Preserve transparency for PNG and GIF
    if ($imageType == IMAGETYPE_PNG || $imageType == IMAGETYPE_GIF) {
        imagealphablending($thumbnail, false);
        imagesavealpha($thumbnail, true);
        $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
        imagefilledrectangle($thumbnail, 0, 0, $thumbnailWidth, $thumbnailHeight, $transparent);
    }

    // Resize image
    imagecopyresampled(
        $thumbnail, $sourceImage,
        0, 0, 0, 0,
        $thumbnailWidth, $thumbnailHeight,
        $originalWidth, $originalHeight
    );

    // Save thumbnail
    switch ($imageType) {
        case IMAGETYPE_JPEG:
            imagejpeg($thumbnail, $thumbnailPath, 85);
            break;
        case IMAGETYPE_PNG:
            imagepng($thumbnail, $thumbnailPath, 8);
            break;
        case IMAGETYPE_GIF:
            imagegif($thumbnail, $thumbnailPath);
            break;
        case IMAGETYPE_WEBP:
            imagewebp($thumbnail, $thumbnailPath, 85);
            break;
    }

    // Clean up
    imagedestroy($sourceImage);
    imagedestroy($thumbnail);

    return true;
}

function generateThumbnailUrl($filePath, $fileType) {
    // SVG files don't need thumbnails, return original
    if ($fileType === 'svg') {
        return UPLOAD_URL . '/' . $filePath;
    }

    if (in_array($fileType, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
        $thumbnailPath = 'thumbnails/' . $filePath;
        $fullThumbnailPath = UPLOAD_PATH . '/' . $thumbnailPath;

        if (file_exists($fullThumbnailPath)) {
            return UPLOAD_URL . '/' . $thumbnailPath;
        }
    }

    // Return original URL if no thumbnail
    return UPLOAD_URL . '/' . $filePath;
}

function getThumbnailPath($filePath) {
    return UPLOAD_PATH . '/thumbnails/' . $filePath;
}

// Batch upload endpoint
if (isset($_GET['action']) && $_GET['action'] === 'batch' && $_SERVER['REQUEST_METHOD'] === 'POST') {
    $user = authenticateRequest();
    if (!$user) {
        jsonResponse(['error' => 'Authentication required'], 401);
    }

    if (!isset($_FILES['files'])) {
        jsonResponse(['error' => 'No files uploaded'], 400);
    }

    $files = $_FILES['files'];
    $directory = isset($_POST['directory']) ? sanitize($_POST['directory']) : 'general';
    $uploadedFiles = [];
    $errors = [];

    for ($i = 0; $i < count($files['name']); $i++) {
        if ($files['error'][$i] === UPLOAD_ERR_OK) {
            $file = [
                'name' => $files['name'][$i],
                'type' => $files['type'][$i],
                'tmp_name' => $files['tmp_name'][$i],
                'error' => $files['error'][$i],
                'size' => $files['size'][$i]
            ];

            try {
                $uploadResult = uploadFile($file, $directory);
                $fileType = strtolower(pathinfo($uploadResult['original_name'], PATHINFO_EXTENSION));

                $mediaData = [
                    'filename' => $uploadResult['filename'],
                    'original_name' => $uploadResult['original_name'],
                    'file_path' => $uploadResult['file_path'],
                    'file_type' => $fileType,
                    'file_size' => $uploadResult['file_size'],
                    'mime_type' => $uploadResult['mime_type'],
                    'uploaded_by' => $user['id']
                ];

                $mediaId = $db->insert('media', $mediaData);
                $media = $db->fetchOne("SELECT * FROM media WHERE id = ?", [$mediaId]);
                $media['url'] = UPLOAD_URL . '/' . $media['file_path'];

                if (in_array($fileType, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
                    generateThumbnail($uploadResult['file_path']);
                    $media['thumbnail_url'] = generateThumbnailUrl($media['file_path'], $media['file_type']);
                } else {
                    // For SVG and other files, use original as thumbnail
                    $media['thumbnail_url'] = generateThumbnailUrl($media['file_path'], $media['file_type']);
                }

                $uploadedFiles[] = $media;

            } catch (Exception $e) {
                $errors[] = [
                    'file' => $files['name'][$i],
                    'error' => $e->getMessage()
                ];
            }
        } else {
            $errors[] = [
                'file' => $files['name'][$i],
                'error' => 'Upload error: ' . $files['error'][$i]
            ];
        }
    }

    jsonResponse([
        'success' => true,
        'uploaded' => $uploadedFiles,
        'errors' => $errors,
        'total_uploaded' => count($uploadedFiles),
        'total_errors' => count($errors)
    ]);
}
?>
