<?php
/**
 * Users Management API
 * Handles user CRUD operations for the mobile app
 */

require_once dirname(__DIR__) . '/config/config.php';

// Set JSON header
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    // All user operations require authentication
    $user = authenticateRequest();
    if (!$user) {
        jsonResponse(['error' => 'Authentication required'], 401);
    }
    
    // Only admins can manage users
    if ($user['role'] !== 'admin') {
        jsonResponse(['error' => 'Admin access required'], 403);
    }
    
    switch ($method) {
        case 'GET':
            if (isset($_GET['action']) && $_GET['action'] === 'profile') {
                handleGetProfile($user);
            } else {
                handleGetUsers();
            }
            break;
            
        case 'POST':
            handleCreateUser($input, $user);
            break;
            
        case 'PUT':
            if (isset($input['action']) && $input['action'] === 'update-profile') {
                handleUpdateProfile($input, $user);
            } else {
                handleUpdateUser($input, $user);
            }
            break;
            
        case 'DELETE':
            handleDeleteUser($user);
            break;
            
        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
    }
} catch (Exception $e) {
    jsonResponse(['error' => $e->getMessage()], 500);
}

function handleGetUsers() {
    global $db;
    
    $page = (int)($_GET['page'] ?? 1);
    $limit = (int)($_GET['limit'] ?? 20);
    $search = $_GET['search'] ?? '';
    $role = $_GET['role'] ?? '';
    
    $offset = ($page - 1) * $limit;
    
    try {
        $where = ['1=1'];
        $params = [];
        
        if ($search) {
            $where[] = '(username LIKE ? OR email LIKE ? OR full_name LIKE ?)';
            $searchTerm = "%$search%";
            $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
        }
        
        if ($role && in_array($role, ['admin', 'editor'])) {
            $where[] = 'role = ?';
            $params[] = $role;
        }
        
        $whereClause = implode(' AND ', $where);
        
        // Get total count
        $totalQuery = "SELECT COUNT(*) as total FROM users WHERE $whereClause";
        $total = $db->fetchOne($totalQuery, $params)['total'];
        
        // Get users (exclude password hash)
        $usersQuery = "SELECT id, username, email, full_name, role, is_active, created_at, updated_at, last_login 
                       FROM users WHERE $whereClause ORDER BY created_at DESC LIMIT $limit OFFSET $offset";
        $users = $db->fetchAll($usersQuery, $params);
        
        // Get role counts
        $roleCounts = [
            'admin' => $db->fetchOne("SELECT COUNT(*) as count FROM users WHERE role = 'admin' AND is_active = 1")['count'],
            'editor' => $db->fetchOne("SELECT COUNT(*) as count FROM users WHERE role = 'editor' AND is_active = 1")['count'],
            'total_active' => $db->fetchOne("SELECT COUNT(*) as count FROM users WHERE is_active = 1")['count']
        ];
        
        jsonResponse([
            'success' => true,
            'users' => $users,
            'role_counts' => $roleCounts,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ]);
        
    } catch (Exception $e) {
        jsonResponse(['error' => 'Failed to fetch users: ' . $e->getMessage()], 500);
    }
}

function handleGetProfile($user) {
    global $db;
    
    try {
        // Get full user profile
        $profile = $db->fetchOne(
            "SELECT id, username, email, full_name, role, is_active, created_at, updated_at, last_login 
             FROM users WHERE id = ?", 
            [$user['id']]
        );
        
        if (!$profile) {
            jsonResponse(['error' => 'Profile not found'], 404);
        }
        
        jsonResponse([
            'success' => true,
            'user' => $profile
        ]);
        
    } catch (Exception $e) {
        jsonResponse(['error' => 'Failed to fetch profile: ' . $e->getMessage()], 500);
    }
}

function handleCreateUser($input, $user) {
    global $db;
    
    // Validate required fields
    $requiredFields = ['username', 'email', 'full_name', 'password', 'role'];
    foreach ($requiredFields as $field) {
        if (!isset($input[$field]) || empty(trim($input[$field]))) {
            jsonResponse(['error' => "Field '$field' is required"], 400);
        }
    }
    
    $username = sanitize($input['username']);
    $email = sanitize($input['email']);
    $fullName = sanitize($input['full_name']);
    $password = $input['password'];
    $role = sanitize($input['role']);
    
    // Validate input
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        jsonResponse(['error' => 'Invalid email address'], 400);
    }
    
    if (strlen($password) < 6) {
        jsonResponse(['error' => 'Password must be at least 6 characters long'], 400);
    }
    
    if (!in_array($role, ['admin', 'editor'])) {
        jsonResponse(['error' => 'Invalid role'], 400);
    }
    
    try {
        // Check if username or email already exists
        $existingUser = $db->fetchOne(
            "SELECT id FROM users WHERE username = ? OR email = ?",
            [$username, $email]
        );
        
        if ($existingUser) {
            jsonResponse(['error' => 'Username or email already exists'], 400);
        }
        
        $userData = [
            'username' => $username,
            'email' => $email,
            'password_hash' => password_hash($password, PASSWORD_DEFAULT),
            'full_name' => $fullName,
            'role' => $role,
            'is_active' => 1,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $userId = $db->insert('users', $userData);
        
        // Get the created user (without password)
        $newUser = $db->fetchOne(
            "SELECT id, username, email, full_name, role, is_active, created_at 
             FROM users WHERE id = ?", 
            [$userId]
        );
        
        jsonResponse([
            'success' => true,
            'message' => 'User created successfully',
            'user' => $newUser
        ]);
        
    } catch (Exception $e) {
        jsonResponse(['error' => 'Failed to create user: ' . $e->getMessage()], 500);
    }
}

function handleUpdateUser($input, $user) {
    global $db;
    
    if (!isset($input['id'])) {
        jsonResponse(['error' => 'User ID is required'], 400);
    }
    
    $userId = (int)$input['id'];
    
    try {
        // Check if user exists
        $targetUser = $db->fetchOne("SELECT * FROM users WHERE id = ?", [$userId]);
        if (!$targetUser) {
            jsonResponse(['error' => 'User not found'], 404);
        }
        
        $updateData = ['updated_at' => date('Y-m-d H:i:s')];
        
        // Update fields if provided
        if (isset($input['username'])) {
            $username = sanitize($input['username']);
            // Check if username already exists (excluding current user)
            $existingUser = $db->fetchOne("SELECT id FROM users WHERE username = ? AND id != ?", [$username, $userId]);
            if ($existingUser) {
                jsonResponse(['error' => 'Username already exists'], 400);
            }
            $updateData['username'] = $username;
        }
        
        if (isset($input['email'])) {
            $email = sanitize($input['email']);
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                jsonResponse(['error' => 'Invalid email address'], 400);
            }
            // Check if email already exists (excluding current user)
            $existingUser = $db->fetchOne("SELECT id FROM users WHERE email = ? AND id != ?", [$email, $userId]);
            if ($existingUser) {
                jsonResponse(['error' => 'Email already exists'], 400);
            }
            $updateData['email'] = $email;
        }
        
        if (isset($input['full_name'])) {
            $updateData['full_name'] = sanitize($input['full_name']);
        }
        
        if (isset($input['password']) && !empty($input['password'])) {
            if (strlen($input['password']) < 6) {
                jsonResponse(['error' => 'Password must be at least 6 characters long'], 400);
            }
            $updateData['password_hash'] = password_hash($input['password'], PASSWORD_DEFAULT);
        }
        
        if (isset($input['role']) && in_array($input['role'], ['admin', 'editor'])) {
            $updateData['role'] = $input['role'];
        }
        
        if (isset($input['is_active'])) {
            $updateData['is_active'] = (bool)$input['is_active'] ? 1 : 0;
        }
        
        $db->update('users', $updateData, 'id = ?', [$userId]);
        
        // Get updated user
        $updatedUser = $db->fetchOne(
            "SELECT id, username, email, full_name, role, is_active, created_at, updated_at, last_login 
             FROM users WHERE id = ?", 
            [$userId]
        );
        
        jsonResponse([
            'success' => true,
            'message' => 'User updated successfully',
            'user' => $updatedUser
        ]);
        
    } catch (Exception $e) {
        jsonResponse(['error' => 'Failed to update user: ' . $e->getMessage()], 500);
    }
}

function handleUpdateProfile($input, $user) {
    global $db;
    
    try {
        $updateData = ['updated_at' => date('Y-m-d H:i:s')];
        
        // Update profile fields
        if (isset($input['full_name'])) {
            $updateData['full_name'] = sanitize($input['full_name']);
        }
        
        if (isset($input['email'])) {
            $email = sanitize($input['email']);
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                jsonResponse(['error' => 'Invalid email address'], 400);
            }
            // Check if email already exists (excluding current user)
            $existingUser = $db->fetchOne("SELECT id FROM users WHERE email = ? AND id != ?", [$email, $user['id']]);
            if ($existingUser) {
                jsonResponse(['error' => 'Email already exists'], 400);
            }
            $updateData['email'] = $email;
        }
        
        $db->update('users', $updateData, 'id = ?', [$user['id']]);
        
        // Get updated profile
        $updatedProfile = $db->fetchOne(
            "SELECT id, username, email, full_name, role, is_active, created_at, updated_at, last_login 
             FROM users WHERE id = ?", 
            [$user['id']]
        );
        
        jsonResponse([
            'success' => true,
            'message' => 'Profile updated successfully',
            'user' => $updatedProfile
        ]);
        
    } catch (Exception $e) {
        jsonResponse(['error' => 'Failed to update profile: ' . $e->getMessage()], 500);
    }
}

function handleDeleteUser($user) {
    global $db;
    
    if (!isset($_GET['id'])) {
        jsonResponse(['error' => 'User ID is required'], 400);
    }
    
    $userId = (int)$_GET['id'];
    
    // Prevent deleting own account
    if ($userId == $user['id']) {
        jsonResponse(['error' => 'Cannot delete your own account'], 400);
    }
    
    try {
        // Check if user exists
        $targetUser = $db->fetchOne("SELECT * FROM users WHERE id = ?", [$userId]);
        if (!$targetUser) {
            jsonResponse(['error' => 'User not found'], 404);
        }
        
        // Soft delete
        $db->update('users', [
            'is_active' => 0,
            'updated_at' => date('Y-m-d H:i:s')
        ], 'id = ?', [$userId]);
        
        jsonResponse([
            'success' => true,
            'message' => 'User deleted successfully'
        ]);
        
    } catch (Exception $e) {
        jsonResponse(['error' => 'Failed to delete user: ' . $e->getMessage()], 500);
    }
}

// Include authentication function from auth.php
function authenticateRequest() {
    global $db;

    $token = getBearerToken();

    if (!$token) {
        return false;
    }

    $hashedToken = hash('sha256', $token);

    // Get token and user info
    $result = $db->fetchOne(
        "SELECT u.id, u.username, u.email, u.full_name, u.role, u.is_active, t.expires_at
         FROM users u
         JOIN api_tokens t ON u.id = t.user_id
         WHERE t.token = ? AND t.is_active = 1 AND u.is_active = 1",
        [$hashedToken]
    );

    if (!$result) {
        return false;
    }

    // Check if token is expired
    if (strtotime($result['expires_at']) < time()) {
        // Deactivate expired token
        $db->update('api_tokens',
            ['is_active' => 0],
            'token = ?',
            [$hashedToken]
        );
        return false;
    }

    return $result;
}

function getBearerToken() {
    $headers = getallheaders();
    
    if (isset($headers['Authorization'])) {
        $matches = [];
        if (preg_match('/Bearer\s+(.*)$/i', $headers['Authorization'], $matches)) {
            return $matches[1];
        }
    }
    
    return null;
}
?>
