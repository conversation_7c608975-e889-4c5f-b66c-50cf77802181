<?php
/**
 * Contact Inquiries API
 * Handles inquiry management for the mobile app
 */

require_once dirname(__DIR__) . '/config/config.php';

// Set JSON header
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    switch ($method) {
        case 'GET':
            $user = authenticateRequest();
            if (!$user) {
                jsonResponse(['error' => 'Authentication required'], 401);
            }
            handleGetInquiries();
            break;
            
        case 'PUT':
            $user = authenticateRequest();
            if (!$user) {
                jsonResponse(['error' => 'Authentication required'], 401);
            }
            handleUpdateInquiry($input, $user);
            break;
            
        case 'DELETE':
            $user = authenticateRequest();
            if (!$user) {
                jsonResponse(['error' => 'Authentication required'], 401);
            }
            handleDeleteInquiry($user);
            break;
            
        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
    }
} catch (Exception $e) {
    jsonResponse(['error' => $e->getMessage()], 500);
}

function handleGetInquiries() {
    global $db;
    
    $page = (int)($_GET['page'] ?? 1);
    $limit = (int)($_GET['limit'] ?? 20);
    $status = $_GET['status'] ?? '';
    $search = $_GET['search'] ?? '';
    
    $offset = ($page - 1) * $limit;
    
    try {
        $where = ['1=1'];
        $params = [];
        
        if ($status && in_array($status, ['new', 'contacted', 'closed'])) {
            $where[] = 'status = ?';
            $params[] = $status;
        }
        
        if ($search) {
            $where[] = '(name LIKE ? OR email LIKE ? OR subject LIKE ? OR message LIKE ?)';
            $searchTerm = "%$search%";
            $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
        }
        
        $whereClause = implode(' AND ', $where);
        
        // Get total count
        $totalQuery = "SELECT COUNT(*) as total FROM contact_inquiries WHERE $whereClause";
        $total = $db->fetchOne($totalQuery, $params)['total'];
        
        // Get inquiries
        $inquiriesQuery = "SELECT * FROM contact_inquiries WHERE $whereClause ORDER BY created_at DESC LIMIT $limit OFFSET $offset";
        $inquiries = $db->fetchAll($inquiriesQuery, $params);
        
        // Get status counts
        $statusCounts = [
            'new' => $db->fetchOne("SELECT COUNT(*) as count FROM contact_inquiries WHERE status = 'new'")['count'],
            'contacted' => $db->fetchOne("SELECT COUNT(*) as count FROM contact_inquiries WHERE status = 'contacted'")['count'],
            'closed' => $db->fetchOne("SELECT COUNT(*) as count FROM contact_inquiries WHERE status = 'closed'")['count']
        ];
        
        jsonResponse([
            'success' => true,
            'inquiries' => $inquiries,
            'status_counts' => $statusCounts,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ]);
        
    } catch (Exception $e) {
        jsonResponse(['error' => 'Failed to fetch inquiries: ' . $e->getMessage()], 500);
    }
}

function handleUpdateInquiry($input, $user) {
    global $db;
    
    if (!isset($input['id'])) {
        jsonResponse(['error' => 'Inquiry ID is required'], 400);
    }
    
    $inquiryId = (int)$input['id'];
    
    try {
        // Check if inquiry exists
        $inquiry = $db->fetchOne("SELECT * FROM contact_inquiries WHERE id = ?", [$inquiryId]);
        if (!$inquiry) {
            jsonResponse(['error' => 'Inquiry not found'], 404);
        }
        
        $updateData = ['updated_at' => date('Y-m-d H:i:s')];
        
        // Update status if provided
        if (isset($input['status']) && in_array($input['status'], ['new', 'contacted', 'closed'])) {
            $updateData['status'] = $input['status'];
        }
        
        // Update notes if provided
        if (isset($input['notes'])) {
            $updateData['notes'] = $input['notes'];
        }
        
        $db->update('contact_inquiries', $updateData, 'id = ?', [$inquiryId]);
        
        // Get updated inquiry
        $updatedInquiry = $db->fetchOne("SELECT * FROM contact_inquiries WHERE id = ?", [$inquiryId]);
        
        jsonResponse([
            'success' => true,
            'message' => 'Inquiry updated successfully',
            'inquiry' => $updatedInquiry
        ]);
        
    } catch (Exception $e) {
        jsonResponse(['error' => 'Failed to update inquiry: ' . $e->getMessage()], 500);
    }
}

function handleDeleteInquiry($user) {
    global $db;
    
    if (!isset($_GET['id'])) {
        jsonResponse(['error' => 'Inquiry ID is required'], 400);
    }
    
    $inquiryId = (int)$_GET['id'];
    
    try {
        // Check if inquiry exists
        $inquiry = $db->fetchOne("SELECT * FROM contact_inquiries WHERE id = ?", [$inquiryId]);
        if (!$inquiry) {
            jsonResponse(['error' => 'Inquiry not found'], 404);
        }
        
        // Delete inquiry
        $db->query("DELETE FROM contact_inquiries WHERE id = ?", [$inquiryId]);
        
        jsonResponse([
            'success' => true,
            'message' => 'Inquiry deleted successfully'
        ]);
        
    } catch (Exception $e) {
        jsonResponse(['error' => 'Failed to delete inquiry: ' . $e->getMessage()], 500);
    }
}

// Include authentication function from auth.php
function authenticateRequest() {
    global $db;

    $token = getBearerToken();

    if (!$token) {
        return false;
    }

    $hashedToken = hash('sha256', $token);

    // Get token and user info
    $result = $db->fetchOne(
        "SELECT u.id, u.username, u.email, u.full_name, u.role, u.is_active, t.expires_at
         FROM users u
         JOIN api_tokens t ON u.id = t.user_id
         WHERE t.token = ? AND t.is_active = 1 AND u.is_active = 1",
        [$hashedToken]
    );

    if (!$result) {
        return false;
    }

    // Check if token is expired
    if (strtotime($result['expires_at']) < time()) {
        // Deactivate expired token
        $db->update('api_tokens',
            ['is_active' => 0],
            'token = ?',
            [$hashedToken]
        );
        return false;
    }

    return $result;
}

function getBearerToken() {
    $headers = getallheaders();
    
    if (isset($headers['Authorization'])) {
        $matches = [];
        if (preg_match('/Bearer\s+(.*)$/i', $headers['Authorization'], $matches)) {
            return $matches[1];
        }
    }
    
    return null;
}
?>
