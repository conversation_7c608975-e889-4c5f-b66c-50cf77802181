<?php
/**
 * Authentication API for Flori Construction Ltd Mobile App
 * Handles login, logout, and token management
 */

require_once dirname(__DIR__) . '/config/config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Only execute request handling if this file is called directly
if (basename($_SERVER['SCRIPT_NAME']) === 'auth.php') {
    // Handle preflight requests
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        exit;
    }

    $method = $_SERVER['REQUEST_METHOD'];
    $input = json_decode(file_get_contents('php://input'), true);

    try {
        switch ($method) {
            case 'POST':
                if (isset($_GET['action'])) {
                    switch ($_GET['action']) {
                        case 'login':
                            handleLogin($input);
                            break;
                        case 'logout':
                            handleLogout();
                            break;
                        case 'refresh':
                            handleRefreshToken();
                            break;
                        default:
                            jsonResponse(['error' => 'Invalid action'], 400);
                    }
                } else {
                    handleLogin($input);
                }
                break;

            case 'GET':
                if (isset($_GET['action'])) {
                    switch ($_GET['action']) {
                        case 'verify':
                            handleVerifyToken();
                            break;
                        case 'ping':
                            jsonResponse(['success' => true, 'message' => 'API is accessible', 'timestamp' => time()]);
                            break;
                        default:
                            jsonResponse(['error' => 'Invalid action'], 400);
                    }
                } else {
                    jsonResponse(['error' => 'Invalid request'], 400);
                }
                break;

            default:
                jsonResponse(['error' => 'Method not allowed'], 405);
        }
    } catch (Exception $e) {
        jsonResponse(['error' => $e->getMessage()], 500);
    }
}

function handleLogin($input) {
    global $db;

    // Log the login attempt for debugging
    error_log("Login attempt: " . json_encode($input));

    if (!isset($input['username']) || !isset($input['password'])) {
        error_log("Missing username or password");
        jsonResponse(['error' => 'Username and password required'], 400);
        return;
    }

    $username = sanitize($input['username']);
    $password = $input['password'];

    // Log the sanitized username
    error_log("Sanitized username: " . $username);

    try {
        // Get user from database
        $user = $db->fetchOne(
            "SELECT id, username, email, password_hash, full_name, role, is_active FROM users WHERE (username = ? OR email = ?) AND is_active = 1",
            [$username, $username]
        );

        error_log("User found: " . ($user ? "Yes" : "No"));

        if (!$user) {
            error_log("User not found for username: " . $username);
            jsonResponse(['error' => 'Invalid credentials'], 401);
            return;
        }

        if (!password_verify($password, $user['password_hash'])) {
            error_log("Password verification failed for user: " . $username);
            jsonResponse(['error' => 'Invalid credentials'], 401);
            return;
        }

        error_log("Login successful for user: " . $username);

    } catch (Exception $e) {
        error_log("Database error during login: " . $e->getMessage());
        jsonResponse(['error' => 'Database error'], 500);
        return;
    }

    // Generate API token
    $token = generateToken(64);
    $expiresAt = date('Y-m-d H:i:s', strtotime('+30 days'));

    // Store token in database
    $db->insert('api_tokens', [
        'user_id' => $user['id'],
        'token' => hash('sha256', $token),
        'expires_at' => $expiresAt
    ]);

    // Update last login
    $db->update('users',
        ['last_login' => date('Y-m-d H:i:s')],
        'id = ?',
        [$user['id']]
    );

    // Remove password hash from response
    unset($user['password_hash']);

    jsonResponse([
        'success' => true,
        'token' => $token,
        'expires_at' => $expiresAt,
        'user' => $user
    ]);
}

function handleLogout() {
    $token = getBearerToken();

    if ($token) {
        global $db;
        $hashedToken = hash('sha256', $token);

        $db->update('api_tokens',
            ['is_active' => 0],
            'token = ?',
            [$hashedToken]
        );
    }

    jsonResponse(['success' => true, 'message' => 'Logged out successfully']);
}

function handleVerifyToken() {
    $user = authenticateRequest();

    if ($user) {
        jsonResponse([
            'success' => true,
            'user' => $user
        ]);
    } else {
        jsonResponse(['error' => 'Invalid token'], 401);
    }
}

function handleRefreshToken() {
    $user = authenticateRequest();

    if (!$user) {
        jsonResponse(['error' => 'Invalid token'], 401);
    }

    global $db;
    $oldToken = getBearerToken();
    $hashedOldToken = hash('sha256', $oldToken);

    // Generate new token
    $newToken = generateToken(64);
    $expiresAt = date('Y-m-d H:i:s', strtotime('+30 days'));

    // Deactivate old token
    $db->update('api_tokens',
        ['is_active' => 0],
        'token = ?',
        [$hashedOldToken]
    );

    // Create new token
    $db->insert('api_tokens', [
        'user_id' => $user['id'],
        'token' => hash('sha256', $newToken),
        'expires_at' => $expiresAt
    ]);

    jsonResponse([
        'success' => true,
        'token' => $newToken,
        'expires_at' => $expiresAt,
        'user' => $user
    ]);
}

function authenticateRequest() {
    global $db;

    $token = getBearerToken();

    if (!$token) {
        return false;
    }

    $hashedToken = hash('sha256', $token);

    // Get token and user info
    $result = $db->fetchOne(
        "SELECT u.id, u.username, u.email, u.full_name, u.role, u.is_active, t.expires_at
         FROM users u
         JOIN api_tokens t ON u.id = t.user_id
         WHERE t.token = ? AND t.is_active = 1 AND u.is_active = 1",
        [$hashedToken]
    );

    if (!$result) {
        return false;
    }

    // Check if token is expired
    if (strtotime($result['expires_at']) < time()) {
        // Deactivate expired token
        $db->update('api_tokens',
            ['is_active' => 0],
            'token = ?',
            [$hashedToken]
        );
        return false;
    }

    return $result;
}

function getBearerToken() {
    $headers = getAuthorizationHeader();

    if (!empty($headers)) {
        if (preg_match('/Bearer\s(\S+)/', $headers, $matches)) {
            return $matches[1];
        }
    }

    return null;
}

function getAuthorizationHeader() {
    $headers = null;

    if (isset($_SERVER['Authorization'])) {
        $headers = trim($_SERVER["Authorization"]);
    } else if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $headers = trim($_SERVER["HTTP_AUTHORIZATION"]);
    } else if (function_exists('apache_request_headers')) {
        $requestHeaders = apache_request_headers();
        $requestHeaders = array_combine(array_map('ucwords', array_keys($requestHeaders)), array_values($requestHeaders));

        if (isset($requestHeaders['Authorization'])) {
            $headers = trim($requestHeaders['Authorization']);
        }
    }

    return $headers;
}

// Cleanup expired tokens (run periodically)
function cleanupExpiredTokens() {
    global $db;

    $db->delete('api_tokens', 'expires_at < NOW() OR is_active = 0');
}

// Run cleanup if requested (only when called directly)
if (basename($_SERVER['SCRIPT_NAME']) === 'auth.php' && isset($_GET['cleanup']) && $_GET['cleanup'] === 'tokens') {
    cleanupExpiredTokens();
    jsonResponse(['success' => true, 'message' => 'Expired tokens cleaned up']);
}
?>
