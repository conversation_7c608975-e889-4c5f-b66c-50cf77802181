<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Icon Generator - Flori Construction</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 30px;
        }
        
        h1 {
            color: #e74c3c;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .generator-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 2px solid #ecf0f1;
            border-radius: 10px;
        }
        
        .icon-preview {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }
        
        .icon-item {
            text-align: center;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .icon-item canvas {
            border: 1px solid #ccc;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        
        .btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #c0392b;
        }
        
        .btn-secondary {
            background: #3498db;
        }
        
        .btn-secondary:hover {
            background: #2980b9;
        }
        
        .progress {
            width: 100%;
            height: 20px;
            background: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #e74c3c, #c0392b);
            width: 0%;
            transition: width 0.3s;
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 PWA Icon Generator</h1>
        <p style="text-align: center; color: #666; font-size: 18px;">Generate all required PWA icons for Flori Construction Admin App</p>
        
        <div class="generator-section">
            <h2>📱 Icon Generation</h2>
            <p>This will generate all required PWA icons with your brand colors and styling.</p>
            
            <div class="progress">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            
            <div id="status" class="status info">
                Ready to generate icons. Click the button below to start.
            </div>
            
            <button class="btn" onclick="generateAllIcons()">🚀 Generate All PWA Icons</button>
            <button class="btn btn-secondary" onclick="downloadAllIcons()" id="downloadBtn" style="display: none;">📥 Download All Icons</button>
        </div>
        
        <div class="generator-section">
            <h2>🖼️ Icon Preview</h2>
            <div id="iconPreview" class="icon-preview">
                <!-- Icons will be generated here -->
            </div>
        </div>
        
        <div class="generator-section">
            <h2>📋 Installation Instructions</h2>
            <div id="instructions" class="hidden">
                <ol>
                    <li>Download all generated icons using the "Download All Icons" button</li>
                    <li>Extract the downloaded ZIP file</li>
                    <li>Copy all icon files to your <code>mobile-app/icons/</code> directory</li>
                    <li>Your PWA is now ready for installation!</li>
                </ol>
                
                <h3>🧪 Testing PWA Installation</h3>
                <ul>
                    <li><strong>Chrome Desktop:</strong> Look for the install icon in the address bar</li>
                    <li><strong>Chrome Mobile:</strong> Use "Add to Home Screen" from the menu</li>
                    <li><strong>Edge:</strong> Click the "Install app" button</li>
                    <li><strong>Safari iOS:</strong> Use "Add to Home Screen" from the share menu</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        const iconSizes = [
            { size: 72, name: 'icon-72x72.png' },
            { size: 96, name: 'icon-96x96.png' },
            { size: 128, name: 'icon-128x128.png' },
            { size: 144, name: 'icon-144x144.png' },
            { size: 152, name: 'icon-152x152.png' },
            { size: 192, name: 'icon-192x192.png' },
            { size: 384, name: 'icon-384x384.png' },
            { size: 512, name: 'icon-512x512.png' }
        ];
        
        const shortcutIcons = [
            { size: 96, name: 'shortcut-add.png', icon: 'plus', color: '#27ae60' },
            { size: 96, name: 'shortcut-upload.png', icon: 'upload', color: '#3498db' }
        ];
        
        let generatedIcons = [];
        
        function updateProgress(percent) {
            document.getElementById('progressBar').style.width = percent + '%';
        }
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        function createIcon(size, isShortcut = false, shortcutData = null) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // Background gradient
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            if (isShortcut && shortcutData) {
                gradient.addColorStop(0, shortcutData.color);
                gradient.addColorStop(1, adjustColor(shortcutData.color, -20));
            } else {
                gradient.addColorStop(0, '#e74c3c');
                gradient.addColorStop(1, '#c0392b');
            }
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // Add rounded corners effect
            ctx.globalCompositeOperation = 'destination-in';
            ctx.beginPath();
            const radius = size * 0.15;
            ctx.roundRect(0, 0, size, size, radius);
            ctx.fill();
            ctx.globalCompositeOperation = 'source-over';
            
            // Add icon content
            ctx.fillStyle = 'white';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            if (isShortcut && shortcutData) {
                // Draw shortcut icon
                const iconSize = size * 0.4;
                ctx.font = `${iconSize}px FontAwesome`;
                
                if (shortcutData.icon === 'plus') {
                    ctx.fillText('+', size/2, size/2);
                } else if (shortcutData.icon === 'upload') {
                    ctx.fillText('↑', size/2, size/2);
                }
            } else {
                // Draw main app icon - "FC" letters
                const fontSize = size * 0.35;
                ctx.font = `bold ${fontSize}px Arial, sans-serif`;
                ctx.fillText('FC', size/2, size/2);
                
                // Add small construction icon
                const smallIconSize = size * 0.15;
                ctx.font = `${smallIconSize}px Arial`;
                ctx.fillText('🏗️', size/2, size * 0.75);
            }
            
            return canvas;
        }
        
        function adjustColor(color, amount) {
            const num = parseInt(color.replace("#", ""), 16);
            const amt = Math.round(2.55 * amount);
            const R = (num >> 16) + amt;
            const G = (num >> 8 & 0x00FF) + amt;
            const B = (num & 0x0000FF) + amt;
            return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
                (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
                (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
        }
        
        async function generateAllIcons() {
            updateStatus('Generating PWA icons...', 'info');
            updateProgress(0);
            
            const preview = document.getElementById('iconPreview');
            preview.innerHTML = '';
            generatedIcons = [];
            
            const totalIcons = iconSizes.length + shortcutIcons.length;
            let completed = 0;
            
            // Generate main app icons
            for (const iconData of iconSizes) {
                const canvas = createIcon(iconData.size);
                
                // Add to preview
                const iconItem = document.createElement('div');
                iconItem.className = 'icon-item';
                iconItem.innerHTML = `
                    <canvas width="${iconData.size}" height="${iconData.size}"></canvas>
                    <div><strong>${iconData.name}</strong></div>
                    <div>${iconData.size}x${iconData.size}</div>
                `;
                
                const previewCanvas = iconItem.querySelector('canvas');
                const previewCtx = previewCanvas.getContext('2d');
                previewCtx.drawImage(canvas, 0, 0);
                
                preview.appendChild(iconItem);
                
                // Convert to blob and store
                canvas.toBlob(blob => {
                    generatedIcons.push({
                        name: iconData.name,
                        blob: blob
                    });
                });
                
                completed++;
                updateProgress((completed / totalIcons) * 100);
                
                // Small delay for smooth animation
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            // Generate shortcut icons
            for (const shortcutData of shortcutIcons) {
                const canvas = createIcon(shortcutData.size, true, shortcutData);
                
                // Add to preview
                const iconItem = document.createElement('div');
                iconItem.className = 'icon-item';
                iconItem.innerHTML = `
                    <canvas width="${shortcutData.size}" height="${shortcutData.size}"></canvas>
                    <div><strong>${shortcutData.name}</strong></div>
                    <div>${shortcutData.size}x${shortcutData.size}</div>
                `;
                
                const previewCanvas = iconItem.querySelector('canvas');
                const previewCtx = previewCanvas.getContext('2d');
                previewCtx.drawImage(canvas, 0, 0);
                
                preview.appendChild(iconItem);
                
                // Convert to blob and store
                canvas.toBlob(blob => {
                    generatedIcons.push({
                        name: shortcutData.name,
                        blob: blob
                    });
                });
                
                completed++;
                updateProgress((completed / totalIcons) * 100);
                
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            updateStatus(`✅ Successfully generated ${totalIcons} PWA icons!`, 'success');
            document.getElementById('downloadBtn').style.display = 'inline-block';
            document.getElementById('instructions').classList.remove('hidden');
        }
        
        async function downloadAllIcons() {
            if (generatedIcons.length === 0) {
                updateStatus('No icons generated yet. Please generate icons first.', 'info');
                return;
            }
            
            updateStatus('Preparing download...', 'info');
            
            // Create ZIP file using JSZip (we'll use a simple approach)
            // For now, we'll download icons individually
            for (const icon of generatedIcons) {
                const url = URL.createObjectURL(icon.blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = icon.name;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                // Small delay between downloads
                await new Promise(resolve => setTimeout(resolve, 200));
            }
            
            updateStatus('✅ All icons downloaded! Copy them to mobile-app/icons/ directory.', 'success');
        }
        
        // Add roundRect polyfill for older browsers
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.beginPath();
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
    </script>
</body>
</html>
