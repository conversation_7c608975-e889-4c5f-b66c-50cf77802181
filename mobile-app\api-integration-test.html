<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Integration Test - Flori Construction</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #e74c3c;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #ecf0f1;
            border-radius: 10px;
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #c0392b;
        }
        
        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .test-results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .auth-section {
            background: #fff3cd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #e74c3c;
        }
        
        .endpoint-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .endpoint-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
        }
        
        .endpoint-card h4 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        
        .endpoint-card .method {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 8px;
        }
        
        .method.get { background: #28a745; color: white; }
        .method.post { background: #007bff; color: white; }
        .method.put { background: #ffc107; color: black; }
        .method.delete { background: #dc3545; color: white; }
        
        .progress {
            width: 100%;
            height: 20px;
            background: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #e74c3c, #c0392b);
            width: 0%;
            transition: width 0.3s;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 API Integration Test</h1>
        <p style="text-align: center; color: #666;">Test and fix API integration issues for Flori Construction Admin</p>
        
        <div class="auth-section">
            <h2>🔐 Authentication</h2>
            <p>First, let's authenticate to get an API token:</p>
            
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" value="admin" placeholder="Enter username">
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" value="admin123" placeholder="Enter password">
            </div>
            
            <button class="btn" onclick="authenticate()">🔑 Login & Get Token</button>
            <div id="auth-status" class="status info">Ready to authenticate</div>
        </div>
        
        <div class="test-section">
            <h2>📊 API Endpoint Tests</h2>
            <p>Test all API endpoints to identify integration issues:</p>
            
            <div class="progress">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            
            <button class="btn" onclick="runAllTests()" id="runTestsBtn" disabled>🚀 Run All API Tests</button>
            <button class="btn btn-secondary" onclick="clearResults()">🗑️ Clear Results</button>
            
            <div id="test-status" class="status info">Authenticate first to run tests</div>
            
            <div class="endpoint-list">
                <div class="endpoint-card">
                    <h4><span class="method get">GET</span> Auth Verify</h4>
                    <p>Test token verification</p>
                    <button class="btn" onclick="testEndpoint('auth', 'verify')" disabled>Test</button>
                </div>
                
                <div class="endpoint-card">
                    <h4><span class="method get">GET</span> Projects List</h4>
                    <p>Fetch all projects</p>
                    <button class="btn" onclick="testEndpoint('projects', 'list')" disabled>Test</button>
                </div>
                
                <div class="endpoint-card">
                    <h4><span class="method get">GET</span> Services List</h4>
                    <p>Fetch all services</p>
                    <button class="btn" onclick="testEndpoint('services', 'list')" disabled>Test</button>
                </div>
                
                <div class="endpoint-card">
                    <h4><span class="method get">GET</span> Media List</h4>
                    <p>Fetch media files</p>
                    <button class="btn" onclick="testEndpoint('media', 'list')" disabled>Test</button>
                </div>
                
                <div class="endpoint-card">
                    <h4><span class="method get">GET</span> Content List</h4>
                    <p>Fetch content sections</p>
                    <button class="btn" onclick="testEndpoint('content', 'list')" disabled>Test</button>
                </div>
                
                <div class="endpoint-card">
                    <h4><span class="method get">GET</span> Mobile Dashboard</h4>
                    <p>Mobile API dashboard data</p>
                    <button class="btn" onclick="testEndpoint('mobile', 'dashboard')" disabled>Test</button>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📋 Test Results</h2>
            <div id="test-results" class="test-results">
                No tests run yet. Click "Run All API Tests" to start testing.
            </div>
        </div>
        
        <div class="test-section">
            <h2>🔧 Common Issues & Fixes</h2>
            <div id="issues-fixes">
                <h3>Potential Issues:</h3>
                <ul>
                    <li><strong>CORS Issues:</strong> Cross-origin requests blocked</li>
                    <li><strong>Authentication:</strong> Token not being sent or recognized</li>
                    <li><strong>Path Issues:</strong> Incorrect API base URL</li>
                    <li><strong>Database Connection:</strong> Database not accessible</li>
                    <li><strong>Missing Headers:</strong> Content-Type or Authorization headers</li>
                </ul>
                
                <h3>Auto-Fix Available:</h3>
                <button class="btn" onclick="autoFixIssues()">🔧 Auto-Fix Common Issues</button>
            </div>
        </div>
    </div>

    <script>
        let apiToken = null;
        let apiBase = null;
        let testResults = [];
        
        // Determine API base URL
        function getApiBaseUrl() {
            const currentLocation = window.location;
            const baseUrl = `${currentLocation.protocol}//${currentLocation.host}`;
            
            if (currentLocation.pathname.includes('/mobile-app/')) {
                return `${baseUrl}${currentLocation.pathname.replace('/mobile-app/', '/').replace(/\/[^\/]*$/, '')}/api`;
            }
            
            return `${baseUrl}/api`;
        }
        
        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }
        
        function updateProgress(percent) {
            document.getElementById('progressBar').style.width = percent + '%';
        }
        
        function addTestResult(test, status, details, response = null) {
            const timestamp = new Date().toLocaleTimeString();
            const result = {
                timestamp,
                test,
                status,
                details,
                response
            };
            
            testResults.push(result);
            updateTestResultsDisplay();
        }
        
        function updateTestResultsDisplay() {
            const resultsDiv = document.getElementById('test-results');
            let output = '';
            
            testResults.forEach(result => {
                const statusIcon = result.status === 'success' ? '✅' : result.status === 'error' ? '❌' : '⚠️';
                output += `[${result.timestamp}] ${statusIcon} ${result.test}: ${result.details}\n`;
                
                if (result.response) {
                    output += `   Response: ${JSON.stringify(result.response, null, 2)}\n`;
                }
                output += '\n';
            });
            
            resultsDiv.textContent = output;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        async function authenticate() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                updateStatus('auth-status', 'Please enter username and password', 'error');
                return;
            }
            
            updateStatus('auth-status', 'Authenticating...', 'info');
            
            try {
                apiBase = getApiBaseUrl();
                console.log('API Base URL:', apiBase);
                
                const response = await fetch(`${apiBase}/auth.php`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                console.log('Auth response status:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('Auth response data:', data);
                
                if (data.success && data.token) {
                    apiToken = data.token;
                    updateStatus('auth-status', `✅ Authentication successful! Token: ${apiToken.substring(0, 20)}...`, 'success');
                    
                    // Enable test buttons
                    document.getElementById('runTestsBtn').disabled = false;
                    document.querySelectorAll('.endpoint-card button').forEach(btn => {
                        btn.disabled = false;
                    });
                    
                    addTestResult('Authentication', 'success', 'Successfully obtained API token', data);
                } else {
                    throw new Error(data.error || 'Authentication failed');
                }
                
            } catch (error) {
                console.error('Authentication error:', error);
                updateStatus('auth-status', `❌ Authentication failed: ${error.message}`, 'error');
                addTestResult('Authentication', 'error', error.message);
            }
        }
        
        async function testEndpoint(category, action) {
            if (!apiToken) {
                updateStatus('test-status', 'Please authenticate first', 'error');
                return;
            }
            
            let url, method = 'GET';
            
            switch (category) {
                case 'auth':
                    url = `${apiBase}/auth.php?action=verify`;
                    break;
                case 'projects':
                    url = `${apiBase}/projects.php`;
                    break;
                case 'services':
                    url = `${apiBase}/services.php`;
                    break;
                case 'media':
                    url = `${apiBase}/media.php`;
                    break;
                case 'content':
                    url = `${apiBase}/content.php`;
                    break;
                case 'mobile':
                    url = `${apiBase}/mobile.php?action=dashboard`;
                    break;
                default:
                    addTestResult(`${category}/${action}`, 'error', 'Unknown endpoint category');
                    return;
            }
            
            try {
                updateStatus('test-status', `Testing ${category}/${action}...`, 'info');
                
                const response = await fetch(url, {
                    method: method,
                    headers: {
                        'Authorization': `Bearer ${apiToken}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok && data.success !== false) {
                    addTestResult(`${category}/${action}`, 'success', `HTTP ${response.status} - Success`, data);
                    updateStatus('test-status', `✅ ${category}/${action} test passed`, 'success');
                } else {
                    addTestResult(`${category}/${action}`, 'error', `HTTP ${response.status} - ${data.error || 'Unknown error'}`, data);
                    updateStatus('test-status', `❌ ${category}/${action} test failed`, 'error');
                }
                
            } catch (error) {
                addTestResult(`${category}/${action}`, 'error', `Network error: ${error.message}`);
                updateStatus('test-status', `❌ ${category}/${action} network error`, 'error');
            }
        }
        
        async function runAllTests() {
            if (!apiToken) {
                updateStatus('test-status', 'Please authenticate first', 'error');
                return;
            }
            
            updateStatus('test-status', 'Running all API tests...', 'info');
            updateProgress(0);
            
            const tests = [
                ['auth', 'verify'],
                ['projects', 'list'],
                ['services', 'list'],
                ['media', 'list'],
                ['content', 'list'],
                ['mobile', 'dashboard']
            ];
            
            for (let i = 0; i < tests.length; i++) {
                const [category, action] = tests[i];
                await testEndpoint(category, action);
                updateProgress(((i + 1) / tests.length) * 100);
                
                // Small delay between tests
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            updateStatus('test-status', '✅ All tests completed', 'success');
            
            // Analyze results
            const successCount = testResults.filter(r => r.status === 'success').length;
            const errorCount = testResults.filter(r => r.status === 'error').length;
            
            addTestResult('Test Summary', 'info', `Completed: ${successCount} passed, ${errorCount} failed`);
        }
        
        function clearResults() {
            testResults = [];
            updateTestResultsDisplay();
            updateProgress(0);
            updateStatus('test-status', 'Results cleared', 'info');
        }
        
        function autoFixIssues() {
            addTestResult('Auto-Fix', 'info', 'Checking for common issues...');
            
            // Check API base URL
            const currentApiBase = getApiBaseUrl();
            addTestResult('Auto-Fix', 'info', `Current API base URL: ${currentApiBase}`);
            
            // Check if we can reach the API
            fetch(`${currentApiBase}/auth.php?action=ping`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        addTestResult('Auto-Fix', 'success', 'API is reachable - no URL issues detected');
                    } else {
                        addTestResult('Auto-Fix', 'warning', 'API responded but with errors');
                    }
                })
                .catch(error => {
                    addTestResult('Auto-Fix', 'error', `API not reachable: ${error.message}`);
                    addTestResult('Auto-Fix', 'info', 'Possible fixes: Check XAMPP is running, verify API path');
                });
            
            // Check localStorage for existing token
            const storedToken = localStorage.getItem('flori_token');
            if (storedToken) {
                addTestResult('Auto-Fix', 'info', `Found stored token: ${storedToken.substring(0, 20)}...`);
            } else {
                addTestResult('Auto-Fix', 'info', 'No stored token found in localStorage');
            }
        }
        
        // Initialize
        window.addEventListener('load', () => {
            apiBase = getApiBaseUrl();
            addTestResult('Initialization', 'info', `API Base URL detected: ${apiBase}`);
        });
    </script>
</body>
</html>
